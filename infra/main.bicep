targetScope = 'subscription'

@description('The location for all resources')
param location string
@description('The environment (dev/prod)')
@allowed([
  'dev'
  'prod'
])
param env string = 'dev'
@description('Principal ID for managed identity')
param principalId string

@description('The number of days to retain log data')
param logAnalyticsRetentionInDays int
@description('The name of the log analytics workspace')
param logAnalyticsName string
@description('The name of the key vault')
param keyVaultName string
@description('The name of the application insights')
param appInsightsName string
@description('Name for OpenAI service')
param openAiName string
@description('Deployment capacity for OpenAI service')
param openAiDeploymentCapacity int
@description('Custom subdomain name for OpenAI service')
param openAiCustomSubDomainName string
@description('Name for container registry')
param containerRegistryName string
@description('Name for container apps environment')
param containerAppEnvName string
@description('Name for NLP Api container app')
param containerAppNLPApiName string
@description('Minimum number of replicas for container app')
param containerAppMinReplicas int
@description('Maximum number of replicas for container app')
param containerAppMaxReplicas int
@description('Container app revision mode')
param containerAppRevisionMode string
@description('NLP Api Container app exists')
param containerAppNLPApiExists bool
@description('New Relic API key')
param newrelicApiKey string

// Resource name variables
var rgName = 'rg-ai-foundry-realplus-${env}'
var rgTags = {
  environment: env
  application: 'RealPlus AI Foundry'
}

// Create resource group
resource resourceGroup 'Microsoft.Resources/resourceGroups@2022-09-01' = {
  name: rgName
  location: location
  tags: rgTags
}

// Deploy Log Analytics
module logAnalytics 'core/monitoring/logAnalytics.bicep' = {
  name: 'logAnalytics-${uniqueString(subscription().id)}'
  scope: resourceGroup
  params: {
    name: logAnalyticsName
    location: location
    env: env
    retentionInDays: logAnalyticsRetentionInDays
  }
}

// Deploy Managed Identity
module managedIdentity 'core/security/managedIdentity.bicep' = {
  name: 'managedIdentity-${uniqueString(subscription().id)}'
  scope: resourceGroup
  params: {
    location: location
    env: env
  }
}

// Deploy Key Vault
module keyVault 'core/security/keyVault.bicep' = {
  name: 'keyVault-${uniqueString(subscription().id)}'
  scope: resourceGroup
  params: {
    location: location
    env: env
    principalId: principalId
    managedIdentityPrincipalId: managedIdentity.outputs.principalId
    name: keyVaultName
  }
}

// Deploy Application Insights
module appInsights 'core/monitoring/appInsights.bicep' = {
  name: 'appInsights-${uniqueString(subscription().id)}'
  scope: resourceGroup
  params: {
    location: location
    env: env
    logAnalyticsWorkspaceId: logAnalytics.outputs.workspaceId
    name: appInsightsName
    keyVaultName: keyVault.outputs.name
  }
}

// Deploy New Relic API Key to Key Vault
module newRelic 'core/monitoring/newrelic.bicep' = {
  name: 'newRelic-${uniqueString(subscription().id)}'
  scope: resourceGroup
  params: {
    keyVaultName: keyVault.outputs.name
    newrelicApiKey: newrelicApiKey
  }
}

// Deploy OpenAI Service
module openAI 'core/ai/openai.bicep' = {
  name: 'openAI-${uniqueString(subscription().id)}'
  scope: resourceGroup
  params: {
    location: location
    env: env
    customSubDomainName: openAiCustomSubDomainName
    managedIdentityId: managedIdentity.outputs.identityId
    managedIdentityPrincipalId: managedIdentity.outputs.principalId
    principalId: principalId
    name: openAiName
    openAiDeploymentCapacity: openAiDeploymentCapacity
    keyVaultName: keyVault.outputs.name
    clientId: managedIdentity.outputs.clientId
  }
}

// Deploy Container Registry
module containerRegistry 'core/registry/containerRegistry.bicep' = {
  name: 'containerRegistry-${uniqueString(subscription().id)}'
  scope: resourceGroup
  params: {
    location: location
    env: env
    //managedIdentityId: managedIdentity.outputs.identityId
    managedIdentityPrincipalId: managedIdentity.outputs.principalId
    logAnalyticsWorkspaceId: logAnalytics.outputs.workspaceId
    name: containerRegistryName
  }
}

// Deploy Container Apps Environment
module containerAppsEnvironment 'app/containerAppEnv.bicep' = {
  name: 'containerAppsEnvironment-${uniqueString(subscription().id)}'
  scope: resourceGroup
  params: {
    location: location
    env: env
    name: containerAppEnvName
    appInsightsName: appInsights.outputs.name
    logAnalyticsName: logAnalytics.outputs.name
  }
}

// Check if NLP Api container app already exists for future deploys
resource existingContainerAppNLPApi 'Microsoft.App/containerApps@2023-05-02-preview' existing = if (containerAppNLPApiExists) {
  scope: resourceGroup
  name: '${containerAppNLPApiName}-${env}'
}
// Deploy NLP Api Container App
module containerAppNLPApi 'app/nlp/containerApp-nlp-api.bicep' = {
  name: 'containerAppNLPApi-${uniqueString(subscription().id)}'
  scope: resourceGroup
  params: {
    location: location
    env: env
    containerAppsEnvironmentId: containerAppsEnvironment.outputs.id
    managedIdentityId: managedIdentity.outputs.identityId
    registryName: containerRegistry.outputs.name
    keyVaultName: keyVault.outputs.name
    openAiEndpoint: openAI.outputs.endpoint
    name: containerAppNLPApiName
    minReplicas: containerAppMinReplicas
    maxReplicas: containerAppMaxReplicas
    revisionsMode: containerAppRevisionMode
    imageName: containerAppNLPApiExists
      ? existingContainerAppNLPApi.?properties.?template.?containers[0].?image ?? 'mcr.microsoft.com/azuredocs/containerapps-helloworld:latest'
      : 'mcr.microsoft.com/azuredocs/containerapps-helloworld:latest'
  }
}

output AZURE_RESOURCE_GROUP string = resourceGroup.name
output AZURE_CONTAINER_REGISTRY_ENDPOINT string = containerRegistry.outputs.loginServer
// output containerAppUrl string = containerApp.outputs.url
// output openAiEndpoint string = openAI.outputs.endpoint
