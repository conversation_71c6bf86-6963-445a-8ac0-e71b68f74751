param location string
param env string
//param managedIdentityId string
param managedIdentityPrincipalId string
param logAnalyticsWorkspaceId string
param name string

var tags = {
  environment: env
  service: 'container-registry'
}

resource containerRegistry 'Microsoft.ContainerRegistry/registries@2023-11-01-preview' = {
  name: '${name}${env}'
  location: location
  tags: tags
  sku: {
    name: 'Basic'
  }
  properties: {
    encryption: {
      status: 'disabled'
    }
    networkRuleBypassOptions: 'AzureServices'
    publicNetworkAccess: 'Enabled'
    zoneRedundancy: 'Disabled'
  }
  // identity: {
  //   type: 'UserAssigned'
  //   userAssignedIdentities: {
  //     '${managedIdentityId}': {}
  //   }
  // }
}

// Assign AcrPull role to the managed identity
resource acrPullRole 'Microsoft.Authorization/roleAssignments@2022-04-01' = {
  name: guid(containerRegistry.id, managedIdentityPrincipalId, 'AcrPull')
  scope: containerRegistry
  properties: {
    roleDefinitionId: subscriptionResourceId(
      'Microsoft.Authorization/roleDefinitions',
      '7f951dda-4ed3-4680-a7ca-43fe172d538d'
    )
    principalId: managedIdentityPrincipalId
    principalType: 'ServicePrincipal'
    description: 'Allows Container App to pull images'
  }
}
// Assign AcrPush role to the managed identity
// resource acrPushRole 'Microsoft.Authorization/roleAssignments@2022-04-01' = {
//   name: guid(containerRegistry.id, managedIdentityPrincipalId, 'AcrPush')
//   scope: containerRegistry
//   properties: {
//     roleDefinitionId: subscriptionResourceId(
//       'Microsoft.Authorization/roleDefinitions',
//       '8311e382-0749-4cb8-b61a-304f252e45ec'
//     )
//     principalId: managedIdentityPrincipalId
//     principalType: 'ServicePrincipal'
//     description: 'Allows managed identity to push images'
//   }
// }

// Add diagnostic settings (need to remove this after the issue is fixed)
// resource diagnosticSettings 'Microsoft.Insights/diagnosticSettings@2021-05-01-preview' = {
//   name: '${containerRegistry.name}-diagnostics'
//   scope: containerRegistry
//   properties: {
//     workspaceId: logAnalyticsWorkspaceId
//     logs: [
//       {
//         category: 'ContainerRegistryRepositoryEvents'
//         enabled: true
//       }
//       {
//         category: 'ContainerRegistryLoginEvents'
//         enabled: true
//       }
//     ]
//     metrics: [
//       {
//         category: 'AllMetrics'
//         enabled: true
//         timeGrain: 'PT1M'
//       }
//     ]
//   }
// }

output id string = containerRegistry.id
output name string = containerRegistry.name
output loginServer string = containerRegistry.properties.loginServer
