# yaml-language-server: $schema=https://raw.githubusercontent.com/Azure/azure-dev/main/schemas/v1.0/azure.yaml.json

name: ai-foundry
metadata:
    template: azd-init@1.11.0
services:
    nlp-api:
        project: src/NLP/RealPlusNLP.Api
        host: containerapp
        language: dotnet
        docker:
          context: ..
          image: ai-foundry/nlp-api-${AZURE_ENV_NAME}
    media-search-web:
        project: src/Media/RealPlusMediaSearch.Web
        host: containerapp
        language: dotnet
        docker:
          context: ..
          image: ai-foundry/media-search-web-${AZURE_ENV_NAME}
    listings-similarity-web:
        project: src/NLP/RealPlusNLP.Similarity.Web
        host: containerapp
        language: dotnet
        docker:
          context: ..
          image: ai-foundry/listings-similarity-web-${AZURE_ENV_NAME}
    realtime-form-web:
        project: src/Lab/RealtimeAI/RealtimeFormApp
        host: containerapp
        language: dotnet
        docker:
          context: ..
          image: ai-foundry/realtime-form-web-${AZURE_ENV_NAME}
    dbchatpro-web:
        project: src/Lab/ChatWithData/DBChatPro
        host: containerapp
        language: dotnet
        docker:
          context: ..
          image: ai-foundry/dbchatpro-web-${AZURE_ENV_NAME}
    mcp-api:
        project: src/MCP/RealPlusMCP.Api
        host: containerapp
        language: dotnet
        docker:
          context: ..
          image: ai-foundry/mcp-api-${AZURE_ENV_NAME}
